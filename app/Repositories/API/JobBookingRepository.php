<?php

namespace App\Repositories\API;

use App\Exceptions\ExceptionHandler;
use App\Helpers\Helpers;
use App\Models\JobBooking;
use App\Models\Asset;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;
use Illuminate\Support\Facades\Log;
use App\Enums\JobBookingStatusEnum;

class JobBookingRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return JobBooking::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Create a new job booking
     *
     * @param array $data
     * @param int|null $userId
     * @return mixed
     */
    public function createJob(array $data, ?int $userId = null)
    {
        DB::beginTransaction();
        try {
            // Map property type if needed
            $propertyType = $data['property']['type'];
            if (in_array($propertyType, ['home', 'business', 'other'])) {
                $propertyType = match($propertyType) {
                    'home' => 'residential',
                    'business' => 'commercial',
                    'other' => 'industrial',
                    default => $propertyType
                };
            }

            // Try to find user by email if userId is not provided
            if ($userId === null && !empty($data['contact']['email'])) {
                Log::info('Looking up user by email', [
                    'email' => $data['contact']['email']
                ]);
                
                $user = User::where('email', $data['contact']['email'])->first();
                
                if ($user) {
                    $userId = $user->id;
                    Log::info('Found user by email', [
                        'userId' => $userId,
                        'email' => $data['contact']['email']
                    ]);
                } else {
                    Log::info('No user found with email', [
                        'email' => $data['contact']['email']
                    ]);
                }
            }

            Log::info('Creating job booking', [
                'userId' => $userId,
                'jobData' => $data
            ]);

            $job = $this->model->create([
                'job_type' => $data['jobType'],
                'property_type' => $propertyType,
                'service_category' => $data['service']['category'],
                'service_tasks' => $data['service']['tasks'],
                'description' => $data['description'] ?? null,
                'schedule_date' => $data['schedule']['date'],
                'time_preference' => $data['schedule']['timePreference'],
                'frequency' => $data['schedule']['frequency'],
                'recurring_frequency' => $data['schedule']['recurringFrequency'] ?? null,
                'address' => $data['location']['address'],
                'city' => $data['location']['city'] ?? null,
                'state' => $data['location']['state'] ?? null,
                'zip_code' => $data['location']['zipCode'] ?? null,
                'contact_name' => $data['contact']['fullName'],
                'contact_email' => $data['contact']['email'],
                'contact_phone' => $data['contact']['phone'],
                'user_id' => $userId,
                'status' => JobBookingStatusEnum::PENDING
            ]);

            // Save user address if user_id exists
            if ($userId) {
                try {
                    Log::info('Attempting to save user address', [
                        'userId' => $userId,
                        'address' => $data['location']['address'],
                        'city' => $data['location']['city'] ?? null,
                        'state' => $data['location']['state'] ?? null,
                        'zip_code' => $data['location']['zipCode'] ?? null,
                    ]);

                    $addressData = [
                        'user_id' => $userId,
                        'address' => $data['location']['address'],
                        'city' => $data['location']['city'] ?? null,
                        'state' => $data['location']['state'] ?? null,
                        'zip_code' => $data['location']['zipCode'] ?? null,
                    ];

                    // Check if address already exists
                    $existingAddress = \App\Models\UserAddress::where($addressData)->first();
                    if ($existingAddress) {
                        Log::info('Address already exists', [
                            'addressId' => $existingAddress->id,
                            'addressData' => $addressData
                        ]);
                    } else {
                        $userAddress = \App\Models\UserAddress::create($addressData);
                        Log::info('Successfully created new address', [
                            'addressId' => $userAddress->id,
                            'addressData' => $addressData
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to save user address', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'addressData' => $addressData ?? null
                    ]);
                    // Don't throw the exception, just log it
                }
            } else {
                Log::info('Skipping address save - no user ID available');
            }

            if (isset($data['assets'])) {
                $assets = Asset::whereIn('uuid', $data['assets'])->get();
                if ($assets->isNotEmpty()) {
                    $job->assets()->sync($assets->pluck('id')->toArray());
                }
                $job->assets()->sync($assets->pluck('id')->toArray());
            }

            DB::commit();
            return $job->load('assets');
        } catch (Exception $e) {
            DB::rollback();
            Log::error('Failed to create job booking', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data ?? null
            ]);
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Get job booking by UUID
     *
     * @param string $jobUuid
     * @return mixed
     */
    public function getByUuid(string $jobUuid)
    {
        try {
            return $this->model
                ->where('job_uuid', $jobUuid)
                ->with(['assets'])
                ->first();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Override update method from BaseRepository to maintain compatibility
     *
     * @param array $attributes
     * @param $id
     * @return mixed
     */
    public function update(array $attributes, $id)
    {
        try {
            $job = $this->find($id);
            if (!$job) {
                throw new Exception("Job not found with ID: {$id}");
            }
            
            return $this->updateJob($job, $attributes);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Update job booking
     *
     * @param JobBooking $job
     * @param array $data
     * @return mixed
     */
    public function updateJob(JobBooking $job, array $data)
    {
        DB::beginTransaction();
        try {
            $job->update([
                'job_type' => $data['jobType'] ?? $job->job_type,
                'property_type' => $data['property']['type'] ?? $job->property_type,
                'service_category' => $data['service']['category'] ?? $job->service_category,
                'schedule_date' => $data['schedule']['date'] ?? $job->schedule_date,
                'time_preference' => $data['schedule']['timePreference'] ?? $job->time_preference,
                'frequency' => $data['schedule']['frequency'] ?? $job->frequency,
                'recurring_frequency' => $data['schedule']['recurringFrequency'] ?? $job->recurring_frequency,
                'address' => $data['location']['address'] ?? $job->address,
                'city' => $data['location']['city'] ?? $job->city,
                'state' => $data['location']['state'] ?? $job->state,
                'zip_code' => $data['location']['zipCode'] ?? $job->zip_code,
                'contact_name' => $data['contact']['fullName'] ?? $job->contact_name,
                'contact_email' => $data['contact']['email'] ?? $job->contact_email,
                'contact_phone' => $data['contact']['phone'] ?? $job->contact_phone,
                'status' => $data['status'] ?? $job->status,
            ]);

            if (!empty($data['assets'])) {
                $assets = Asset::whereIn('uuid', $data['assets'])->get();
                if ($assets->isEmpty()) {
                    throw new Exception('No assets found with provided UUIDs');
                }
                $job->assets()->attach($assets->pluck('id')->toArray());
            }

            DB::commit();
            return $job->fresh()->load('assets');
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Override delete method from BaseRepository to maintain compatibility
     *
     * @param $id
     * @return bool
     */
    public function delete($id)
    {
        try {
            $job = $this->find($id);
            if (!$job) {
                throw new Exception("Job not found with ID: {$id}");
            }
            
            return $this->deleteJob($job);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Delete job booking
     *
     * @param JobBooking $job
     * @return bool
     */
    public function deleteJob(JobBooking $job)
    {
        DB::beginTransaction();
        try {
            // Then delete the job
            $job->delete();
            
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollback();
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Get all job bookings with filters
     *
     * @param array $filters
     * @param int|null $perPage
     * @return mixed
     */
    public function getAll(array $filters = [], ?int $perPage = 15)
    {
        try {
            $query = $this->model->newQuery();
            
            // Apply filters
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (!empty($filters['user_id'])) {
                $query->where('user_id', $filters['user_id']);
            }
            
            if (!empty($filters['job_type'])) {
                $query->where('job_type', $filters['job_type']);
            }
            
            if (!empty($filters['service_category'])) {
                $query->where('service_category', $filters['service_category']);
            }
            
            // Sort by creation date (newest first by default)
            $query->orderBy('created_at', 'desc');
            
            // Paginate results if requested
            if ($perPage) {
                return $query->paginate($perPage);
            }
            
            return $query->get();
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Extract file path from URL
     *
     * @param string $url
     * @return string
     */
    private function extractPathFromUrl($url)
    {
        // If URL is already a path (doesn't start with http or https), return as is
        if (!preg_match('/^https?:\/\//', $url)) {
            return $url;
        }
        
        // Remove the domain and storage part from the URL
        // Example: https://example.com/storage/job-media/file.jpg -> job-media/file.jpg
        $storagePath = 'storage/';
        $position = strpos($url, $storagePath);
        
        if ($position !== false) {
            return substr($url, $position + strlen($storagePath));
        }
        
        // If storage path is not found, just return the basename as fallback
        return basename($url);
    }

    public function mapJobBookingsToUserByEmail(string $email, int $userId): int
    {
        return $this->model->where('contact_email', $email)
            ->whereNull('user_id')
            ->update(['user_id' => $userId]);
    }
} 